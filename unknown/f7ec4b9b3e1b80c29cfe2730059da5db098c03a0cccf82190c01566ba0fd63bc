{"logs": [{"outputFile": "com.Zumbla.Burst2025.app-mergeDebugResources-54:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3930e81a22611b2ddd91d6283578bd0f\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3507,3604,3706,3807,3904,4011,4119,14499", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3599,3701,3802,3899,4006,4114,4236,14595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\66b37f3f60883449bfc7382cd0423a97\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,14174", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,14254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b9dd55976dd5ae39f237e6587c02b703\\transformed\\browser-1.4.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "67,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "7001,7415,7520,7631", "endColumns": "102,104,110,104", "endOffsets": "7099,7515,7626,7731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38366cef8399d337aed3da4a7ed40977\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5626", "endColumns": "144", "endOffsets": "5766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bb16bb95ce2c158213264f7a72032a8\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,3417,4241,4346,4482,7104,7164,7316,7736,7804,7863,7952,8020,8087,8150,8225,8293,8347,8467,8525,8587,8641,8716,8858,8948,9026,9120,9203,9288,9433,9517,9600,9746,9842,9919,9977,10028,10094,10168,10246,10317,10403,10477,10556,10629,10701,10817,10921,10994,11093,11193,11267,11342,11449,11501,11590,11657,11748,11842,11904,11968,12031,12101,12220,12325,12434,12534,12596,13530,14259,14346,14424", "endLines": "5,33,34,35,36,37,45,46,47,68,69,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,146,156,157,158", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "325,3149,3229,3315,3412,3502,4341,4477,4562,7159,7224,7410,7799,7858,7947,8015,8082,8145,8220,8288,8342,8462,8520,8582,8636,8711,8853,8943,9021,9115,9198,9283,9428,9512,9595,9741,9837,9914,9972,10023,10089,10163,10241,10312,10398,10472,10551,10624,10696,10812,10916,10989,11088,11188,11262,11337,11444,11496,11585,11652,11743,11837,11899,11963,12026,12096,12215,12320,12429,12529,12591,12646,13610,14341,14419,14494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5fb92ec23f9579e12652e527e2b5ff5e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4567,4674,4850,4988,5097,5255,5391,5513,5771,5950,6057,6235,6373,6535,6714,6782,6848", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "4669,4845,4983,5092,5250,5386,5508,5621,5945,6052,6230,6368,6530,6709,6777,6843,6924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0d3df4af221a288d9bcf50e45d897280\\transformed\\preference-1.2.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,346,485,654,739", "endColumns": "71,86,81,138,168,84,80", "endOffsets": "172,259,341,480,649,734,815"}, "to": {"startLines": "66,70,138,147,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6929,7229,12814,13615,14600,14769,14854", "endColumns": "71,86,81,138,168,84,80", "endOffsets": "6996,7311,12891,13749,14764,14849,14930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a96715ba9df777a8e995ff71e6bff2\\transformed\\jetified-play-services-ads-22.1.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,350,425,522,572,693,754,872,956,1002,1094,1131,1168,1225,1306,1348", "endColumns": "45,48,55,74,96,49,120,60,117,83,45,91,36,36,56,80,41,55", "endOffsets": "244,293,349,424,521,571,692,753,871,955,1001,1093,1130,1167,1224,1305,1347,1403"}, "to": {"startLines": "135,136,137,139,140,141,142,143,144,145,148,149,150,151,152,153,154,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12651,12701,12754,12896,12975,13076,13130,13255,13320,13442,13754,13804,13900,13941,13982,14043,14128,14935", "endColumns": "49,52,59,78,100,53,124,64,121,87,49,95,40,40,60,84,45,59", "endOffsets": "12696,12749,12809,12970,13071,13125,13250,13315,13437,13525,13799,13895,13936,13977,14038,14123,14169,14990"}}]}]}