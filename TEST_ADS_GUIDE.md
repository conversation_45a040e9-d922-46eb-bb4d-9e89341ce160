# 🧪 دليل الإعلانات التجريبية - Test Ads Guide

## 📋 **نظرة عامة**

تم تفعيل الإعلانات التجريبية في التطبيق لاختبار المشكلة أثناء حل مشاكل AdMob. هذا يسمح لك بتشغيل التطبيق واختبار الإعلانات بدون التأثير على حسابك في AdMob.

## ⚙️ **الإعدادات الحالية**

### 1. **وضع الاختبار مُفعل:**
```xml
<bool name="is_testing">true</bool>
```

### 2. **معرفات الإعلانات التجريبية:**
- **Banner Test ID:** `ca-app-pub-3940256099942544/6300978111`
- **Interstitial Test ID:** `ca-app-pub-3940256099942544/1033173712`

### 3. **معرفات الإعلانات الإنتاجية (محفوظة):**
- **Banner Production ID:** `ca-app-pub-7841751633097845/1754562616`
- **Interstitial Production ID:** `ca-app-pub-7841751633097845/3709823870`

## 🔍 **كيفية التعرف على الإعلانات التجريبية**

عند تشغيل التطبيق، ستظهر الإعلانات التجريبية مع:
- **علامة "Test Ad"** في الزاوية
- **خلفية مختلفة** (عادة بيضاء أو رمادية)
- **نص "Test Ad" أو "Sample Ad"**

## 📱 **اختبار الإعلانات**

### **Banner Ads:**
- تظهر في أسفل الشاشة
- تحديث تلقائي كل 30-60 ثانية
- لا تؤثر على الإيرادات

### **Interstitial Ads:**
- تظهر كل مرتين (intervalAds: 2)
- في النقاط التالية:
  - اختيار المستوى
  - النجاح في المستوى
  - الخسارة في المستوى
  - بداية موسم جديد

## 🔧 **التبديل بين الوضعين**

### **للتبديل إلى الإعلانات التجريبية:**
```xml
<bool name="is_testing">true</bool>
<string name="id_banner">ca-app-pub-3940256099942544/6300978111</string>
<string name="id_inter">ca-app-pub-3940256099942544/1033173712</string>
```

### **للعودة إلى الإعلانات الإنتاجية:**
```xml
<bool name="is_testing">false</bool>
<string name="id_banner">ca-app-pub-7841751633097845/1754562616</string>
<string name="id_inter">ca-app-pub-7841751633097845/3709823870</string>
```

## 📊 **مراقبة الإعلانات**

### **في Logcat ستجد:**
```
AdMob_Test: TESTING MODE ENABLED
AdMob_Test: DEVICE ID : [YOUR_DEVICE_ID]
AdMob_Test: Using Test Ad IDs
Jacob: onAdLoaded
Jacob: The ad was shown.
```

## ⚠️ **تحذيرات مهمة**

1. **لا تنشر التطبيق** مع الإعلانات التجريبية مُفعلة
2. **لا تنقر** على الإعلانات التجريبية بكثرة
3. **تأكد من التبديل** إلى الإعلانات الإنتاجية قبل النشر
4. **احتفظ بنسخة احتياطية** من معرفات الإعلانات الإنتاجية

## 🚀 **خطوات حل مشكلة AdMob**

### **1. اختبار مع الإعلانات التجريبية:**
- تشغيل التطبيق
- التأكد من ظهور الإعلانات
- اختبار جميع النقاط

### **2. مراجعة الكود:**
- التأكد من عدم وجود تشفير Base64
- فحص تكرار الإعلانات
- مراجعة توزيع الإعلانات

### **3. بناء APK للاختبار:**
```bash
./gradlew assembleDebug
```

### **4. العودة للإنتاج:**
- تغيير `is_testing` إلى `false`
- تحديث معرفات الإعلانات
- بناء APK للإنتاج

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. تحقق من Logcat للأخطاء
2. تأكد من الاتصال بالإنترنت
3. راجع إعدادات AdMob
4. تحقق من صحة معرفات الإعلانات

---

**آخر تحديث:** تم تفعيل الإعلانات التجريبية بنجاح ✅
